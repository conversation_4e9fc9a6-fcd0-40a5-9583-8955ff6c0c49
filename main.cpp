#define GL_SILENCE_DEPRECATION
#define IMGUI_DEFINE_MATH_OPERATORS

#include "IconsFontAwesome6.h"

#include "imgui.h"
#include "imgui_internal.h"
#include "imgui_impl_glfw.h"
#include "imgui_impl_opengl3.h"
#include <GLFW/glfw3.h>

#include <algorithm>
#include <fstream>
#include <filesystem>
#include <iostream>
#include <sstream>
#include <thread>
#include <string>
#include <atomic>
#include <vector>
#include <queue>
#include <mutex>
#include <map>
#include <chrono>
#include <functional>
#include <fmt/format.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <netinet/tcp.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

// Configuration
// Asset and file path configuration
#define FONT_PATH_REGULAR           "fonts/DroidSerif-Regular.ttf"
#define FONT_PATH_ICONS             "fonts/Font Awesome 6 Free-Solid-900.otf"

// File extensions for scanning
#define MESH_EXTENSIONS             {"*.obj", "*.glb", "*.gltf"} // for when we can load objects
#define JSON_EXTENSIONS             {"*.json"}

// Network configuration
#define SERVER_PORT                 3000
#define SERVER_ADDRESS              "127.0.0.1"
#define HEARTBEAT_TIMEOUT           10.0f

// UI configuration
#define WINDOW_WIDTH                720
#define WINDOW_HEIGHT               960
#define WINDOW_TITLE                "Renderer V1"
#define FONT_SIZE_REGULAR           16.0f
#define FONT_SIZE_LARGE             20.0f
#define FONT_SIZE_SMALL             12.0f

#define UI_LOG_VERBOSE              0 // 0 = off, 1 = on
#define UI_LOG_ERRORS               1 // 0 = off (silent), 1 = print to std::cerr

// Error logging macro (stream-style usage)
// Example: UI_ERROR("Failed: " << reason);
#if UI_LOG_ERRORS
#define UI_ERROR(expr) do { std::cerr << expr << std::endl; } while (0)
#else
#define UI_ERROR(expr) do { } while (0)
#endif

// ------------------------------------------------------------------------------------------------
// RenderingClient class - complete implementation
class RenderingClient {
public:
    RenderingClient(const std::string& host = SERVER_ADDRESS, int port = SERVER_PORT, 
                   float heartbeat_timeout = HEARTBEAT_TIMEOUT);
    ~RenderingClient();

    // Public API
    bool connected() const;
    void start();
    void stop();
    bool send_text(const std::string& text, const std::string& encoding = "utf-8");
    bool send_command(const std::string& command);

    // Callback setters
    void set_on_message(std::function<void(const std::string&)> callback);
    void set_on_status(std::function<void(const std::string&)> callback);

private:
    // Connection details
    std::string host_;
    int port_;
    SOCKET sock_;
    std::atomic<bool> connected_;
    std::mutex lock_;
    std::chrono::steady_clock::time_point last_rx_;

    // Thread control
    bool auto_reconnect_;
    float heartbeat_timeout_;
    std::thread reader_thread_;
    std::thread heartbeat_thread_;
    std::atomic<bool> reader_stop_;
    std::atomic<bool> heartbeat_stop_;
    std::atomic<bool> running_;

    // Callbacks
    std::function<void(const std::string&)> on_message_;
    std::function<void(const std::string&)> on_status_;

    // Helper methods
    void log(const std::string& msg);
    void enable_keepalive(SOCKET s);
    void connect_internal(bool start_threads);
    void start_threads_internal();
    void join_threads();
    void close_transport();
    void send_internal(const std::vector<uint8_t>& payload);
    std::vector<uint8_t> recv_exact(size_t n);
    std::vector<uint8_t> recv_frame();

    // Thread functions
    void reader_loop();
    void heartbeat_loop();
};

// ------------------------------------------------------------------------------------------------
// RenderingClient Implementation
RenderingClient::RenderingClient(const std::string& host, int port, float heartbeat_timeout)
    : host_(host), port_(port), sock_(INVALID_SOCKET), connected_(false),
      auto_reconnect_(true), heartbeat_timeout_(heartbeat_timeout),
      reader_stop_(false), heartbeat_stop_(false), running_(true) {
    
    last_rx_ = std::chrono::steady_clock::now();
    
    // Default callbacks
    on_message_ = [](const std::string& s) { 
#if UI_LOG_VERBOSE
        std::cout << "Server: " << s << std::endl; 
#endif
    };
    on_status_ = [](const std::string& s) { 
#if UI_LOG_VERBOSE
        std::cout << "[status] " << s << std::endl; 
#endif
    };

#ifdef _WIN32
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
}

RenderingClient::~RenderingClient() {
    stop();
#ifdef _WIN32
    WSACleanup();
#endif
}

bool RenderingClient::connected() const {
    return connected_.load();
}

void RenderingClient::start() {
    connect_internal(false);
    start_threads_internal();
}

void RenderingClient::stop() {
    auto_reconnect_ = false;
    running_ = false;
    heartbeat_stop_ = true;
    reader_stop_ = true;
    close_transport();
    join_threads();
}

bool RenderingClient::send_text(const std::string& text, const std::string& encoding) {
    try {
        std::vector<uint8_t> payload(text.begin(), text.end());
        send_internal(payload);
        return true;
    } catch (const std::exception& e) {
        log("Error sending message: " + std::string(e.what()));
        return false;
    }
}

bool RenderingClient::send_command(const std::string& command) {
    return send_text(command);
}

void RenderingClient::set_on_message(std::function<void(const std::string&)> callback) {
    on_message_ = callback;
}

void RenderingClient::set_on_status(std::function<void(const std::string&)> callback) {
    on_status_ = callback;
}

void RenderingClient::log(const std::string& msg) {
    on_status_(msg);
}

void RenderingClient::enable_keepalive(SOCKET s) {
#ifdef _WIN32
    DWORD keepalive = 1;
    setsockopt(s, SOL_SOCKET, SO_KEEPALIVE, (const char*)&keepalive, sizeof(keepalive));
#else
    int keepalive = 1;
    setsockopt(s, SOL_SOCKET, SO_KEEPALIVE, &keepalive, sizeof(keepalive));
    
    int keepidle = 30;
    int keepintvl = 10;
    int keepcnt = 3;
    setsockopt(s, IPPROTO_TCP, TCP_KEEPIDLE, &keepidle, sizeof(keepidle));
    setsockopt(s, IPPROTO_TCP, TCP_KEEPINTVL, &keepintvl, sizeof(keepintvl));
    setsockopt(s, IPPROTO_TCP, TCP_KEEPCNT, &keepcnt, sizeof(keepcnt));
#endif
}

void RenderingClient::connect_internal(bool start_threads) {
    {
        std::lock_guard<std::mutex> lock(lock_);
        if (connected_ && sock_ != INVALID_SOCKET) {
            return;
        }
    }
    
    SOCKET s = socket(AF_INET, SOCK_STREAM, 0);
    if (s == INVALID_SOCKET) {
        throw std::runtime_error("Failed to create socket");
    }
    
    // Set timeout
#ifdef _WIN32
    DWORD timeout = HEARTBEAT_TIMEOUT * 1000; // 5 seconds
    setsockopt(s, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
    setsockopt(s, SOL_SOCKET, SO_SNDTIMEO, (const char*)&timeout, sizeof(timeout));
#else
    struct timeval timeout;
    timeout.tv_sec = HEARTBEAT_TIMEOUT;
    timeout.tv_usec = 0;
    setsockopt(s, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
    setsockopt(s, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
#endif
    
    enable_keepalive(s);
    
    sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port_);
    inet_pton(AF_INET, host_.c_str(), &server_addr.sin_addr);
    
    if (connect(s, (sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        closesocket(s);
        return; // Let heartbeat loop handle retry
    }
    
    {
        std::lock_guard<std::mutex> lock(lock_);
        sock_ = s;
        connected_ = true;
        last_rx_ = std::chrono::steady_clock::now();
    }
    
    log("Connected to rendering server at " + host_ + ":" + std::to_string(port_));
    
    if (start_threads) {
        start_threads_internal();
    }
}

void RenderingClient::start_threads_internal() {
    if (!reader_thread_.joinable()) {
        reader_stop_ = false;
        reader_thread_ = std::thread(&RenderingClient::reader_loop, this);
    }
    
    if (!heartbeat_thread_.joinable()) {
        heartbeat_stop_ = false;
        heartbeat_thread_ = std::thread(&RenderingClient::heartbeat_loop, this);
    }
}

void RenderingClient::join_threads() {
    if (reader_thread_.joinable()) {
        reader_thread_.join();
    }
    if (heartbeat_thread_.joinable()) {
        heartbeat_thread_.join();
    }
}

void RenderingClient::close_transport() {
    SOCKET s;
    bool was_connected;
    
    {
        std::lock_guard<std::mutex> lock(lock_);
        s = sock_;
        was_connected = connected_;
        sock_ = INVALID_SOCKET;
        connected_ = false;
    }
    
    if (s != INVALID_SOCKET) {
#ifdef _WIN32
        shutdown(s, SD_BOTH);
#else
        shutdown(s, SHUT_RDWR);
#endif
        closesocket(s);
    }
    
    if (was_connected) {
        log("Disconnected");
    }
}

void RenderingClient::send_internal(const std::vector<uint8_t>& payload) {
    uint32_t length = htonl(payload.size());
    
    std::lock_guard<std::mutex> lock(lock_);
    if (!connected_ || sock_ == INVALID_SOCKET) {
        throw std::runtime_error("not connected");
    }
    
    // Send header
    if (send(sock_, reinterpret_cast<const char*>(&length), sizeof(length), 0) == SOCKET_ERROR) {
        throw std::runtime_error("send failed");
    }
    
    // Send payload
    if (!payload.empty()) {
        if (send(sock_, reinterpret_cast<const char*>(payload.data()), payload.size(), 0) == SOCKET_ERROR) {
            throw std::runtime_error("send failed");
        }
    }
    
    // Reset heartbeat timer on outgoing traffic
    // This prevents unnecessary reconnections when the connection is healthy
    // The server only sends heartbeats during idle periods, so we need to
    // account for our own outgoing traffic
    last_rx_ = std::chrono::steady_clock::now();
    
    // Debug logging for heartbeat behavior (only in verbose mode)
    if (UI_LOG_VERBOSE) {
        log("Sent message, reset heartbeat timer");
    }
}

std::vector<uint8_t> RenderingClient::recv_exact(size_t n) {
    std::vector<uint8_t> buffer(n);
    size_t received = 0;
    
    while (received < n) {
        SOCKET sock;
        bool is_connected;
        {
            std::lock_guard<std::mutex> lock(lock_);
            sock = sock_;
            is_connected = connected_;
        }
        
        if (!is_connected || sock == INVALID_SOCKET) {
            throw std::runtime_error("not connected");
        }
        
        int result = recv(sock, reinterpret_cast<char*>(buffer.data() + received), n - received, 0);
        if (result <= 0) {
            throw std::runtime_error("peer closed");
        }
        received += result;
    }
    
    return buffer;
}

std::vector<uint8_t> RenderingClient::recv_frame() {
    // Receive header
    auto header = recv_exact(4);
    uint32_t length = ntohl(*reinterpret_cast<uint32_t*>(header.data()));
    
    if (length == 0) {
        return {};
    }
    
    // Receive body
    return recv_exact(length);
}

void RenderingClient::reader_loop() {
    while (!reader_stop_ && running_) {
        if (!connected()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
            continue;
        }
        
        try {
            auto msg_data = recv_frame();
            // Reset heartbeat timer on incoming traffic
            // This works together with the reset in send_internal() to provide
            // bidirectional heartbeat tracking that matches the server's behavior
            last_rx_ = std::chrono::steady_clock::now();
            
            if (msg_data.empty()) {
                continue;
            }
            
            std::string text(msg_data.begin(), msg_data.end());
            
            // Handle special messages
            if (text == "heartbeat") {
                continue;
            }
            if (text == "shutdown") {
                std::cout << "Server is shutting down." << std::endl;
                close_transport();
                continue;
            }
            
            // Call user callback
            on_message_(text);
            
        } catch (const std::exception&) {
            close_transport();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
}

void RenderingClient::heartbeat_loop() {
    int attempt = 0;
    int last_logged_attempt = 0;
    
    while (!heartbeat_stop_ && running_) {
        if (!connected_ && auto_reconnect_) {
            attempt++;
            float delay = std::min<float>(3.0f, 1.0f * std::pow(2, attempt - 1));
            
            if (attempt != last_logged_attempt) {
                log("Reconnecting in " + std::to_string(delay) + "s (attempt " + std::to_string(attempt) + ")");
                last_logged_attempt = attempt;
            }
            
            // Sleep with cancellation check
            auto end = std::chrono::steady_clock::now() + std::chrono::milliseconds(static_cast<int>(delay * 1000));
            while (!heartbeat_stop_ && running_ && std::chrono::steady_clock::now() < end) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
            if (heartbeat_stop_ || !running_) break;
            
            try {
                connect_internal(false);
                if (connected_) {
                    if (attempt > 0) {
                        log("Reconnected");
                    }
                    attempt = 0;
                    last_logged_attempt = 0;
                }
            } catch (const std::exception&) {
                // Stay disconnected, will retry
            }
        } else if (connected_) {
            auto now = std::chrono::steady_clock::now();
            auto idle = std::chrono::duration_cast<std::chrono::seconds>(now - last_rx_).count();
            
            // Check for heartbeat timeout
            // Note: last_rx_ is reset on both incoming messages AND outgoing traffic
            // This matches the server's behavior of only sending heartbeats during idle periods
            if (idle > heartbeat_timeout_) {
                log("Heartbeat timeout (idle " + std::to_string(idle) + "s) -> reconnect");
                close_transport();
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(250));
    }
}

//------------------------------ tiny math ---------------------
struct Vec3 {
    float x,y,z;
    Vec3()                         :x(0),y(0),z(0) {}
    Vec3(float X,float Y,float Z)  :x(X),y(Y),z(Z){}
    Vec3 operator+(const Vec3& v)const{return {x+v.x,y+v.y,z+v.z};}
    Vec3 operator-(const Vec3& v)const{return {x-v.x,y-v.y,z-v.z};}
    Vec3 operator*(float s) const  {return {x*s,y*s,z*s};}
    Vec3& operator+=(const Vec3& v){x+=v.x;y+=v.y;z+=v.z;return *this;}
    float  dot (const Vec3& v)const{return x*v.x+y*v.y+z*v.z;}
    Vec3   cross(const Vec3& v)const{return {y*v.z-z*v.y, z*v.x-x*v.z, x*v.y-y*v.x};}
    float  len ()const             {return std::sqrt(dot(*this));}
    Vec3   norm()const             {float l=len();return l>1e-6f?(*this)*(1.f/l):Vec3();}
};

// ------------------------------------------------------------------------------------------------
struct MeshEntity {
    std::string name;
    Vec3 position;
    Vec3 rotation;
    Vec3 scale;
};

struct LightEntity {
    std::string name;
    Vec3 position;
    Vec3 color;
    float intensity;
};

// ------------------------------------------------------------------------------------------------
// AppState structure
struct AppState {
    std::unique_ptr<RenderingClient> client;
    std::atomic<bool> running{true};
    
    // Thread-safe message queue for UI updates
    std::queue<std::string> ui_message_queue;
    std::mutex ui_queue_mutex;

    std::vector<std::string> sceneFiles;
    int selectedSceneIndex = 0;

    std::vector<MeshEntity> meshEntities;
    std::vector<LightEntity> lightEntities;

    std::string assetRoot = "";

    Vec3 cameraPos      = { 0.0f, 0.0f, 1.0f };
	Vec3 cameraRotation = { 0.0f, 180.0f, 0.0f }; // pitch, yaw, roll
	float cameraFov     = 25.0f; // Field of view in degrees

    // initial camera values
	Vec3 initialCameraPos = { 0.0f, 0.0f, 1.0f };
	Vec3 initialCameraRotation = { 0.0f, 180.0f, 0.0f }; // pitch, yaw, roll
	float initialCameraFov = 25.0f; // Field of view in degrees

    // Rendering options
    int debugModeIdx = 0;
    int renderTypeIdx = 0;
};

// ------------------------------------------------------------------------------------------------
std::vector<std::string> scan_files(const std::string& base_path = ".", 
                                   const std::vector<std::string>& extensions = {"*.xml"}) {
    std::vector<std::string> files;
    
    for (const auto& extension : extensions) {
        // Remove the * from extension for std::filesystem
        std::string ext = extension.substr(1); // Remove '*'
        
        try {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(base_path)) {
                if (entry.is_regular_file()) {
                    std::string file_ext = entry.path().extension().string();
                    
                    // Check if extension matches and skip hidden files/directories
                    if (file_ext == ext) {
                        std::string rel_path = std::filesystem::relative(entry.path(), base_path).string();
                        
                        // Skip files in hidden directories or that start with .
                        bool skip = false;
                        std::filesystem::path path_check(rel_path);
                        for (const auto& part : path_check) {
                            std::string part_str = part.string();
                            if (!part_str.empty() && part_str[0] == '.') {
                                skip = true;
                                break;
                            }
                        }
                        
                        if (!skip) {
                            files.push_back(rel_path);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            UI_ERROR("Error scanning for " << extension << ": " << e.what());
        }
    }
    
    // Sort for consistent ordering
    std::sort(files.begin(), files.end());
    return files;
}

// ------------------------------------------------------------------------------------------------
void uiVSeparator() {
	ImGui::SameLine();
	ImGui::SeparatorEx(ImGuiSeparatorFlags_Vertical);
	ImGui::SameLine();
}

// ------------------------------------------------------------------------------------------------
void uiPushFlatStyle() {
	ImGui::PushStyleColor(ImGuiCol_Button,			ImVec4(1.0f, 0.4f, 0.4f, 0.0f));
	ImGui::PushStyleColor(ImGuiCol_ButtonHovered,	ImVec4(1.0f, 1.0f, 1.0f, 0.5f));
	ImGui::PushStyleColor(ImGuiCol_ButtonActive,	ImVec4(1.0f, 1.0f, 1.0f, 0.5f));
	ImGui::PushStyleColor(ImGuiCol_Separator,		ImVec4(0.5f, 0.5f, 0.5f, 0.2f));
	ImGui::PushStyleColor(ImGuiCol_Header,			ImVec4(.4f, .4f, .4f, 0.f));
	ImGui::PushStyleColor(ImGuiCol_HeaderHovered,	ImVec4(1.f, 1.f, 1.f, 1.f));
	ImGui::PushStyleColor(ImGuiCol_HeaderActive,	ImVec4(1.f, 1.f, 1.f, 1.f));
	ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 0.0f);
	ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(7, 5));
}

// ------------------------------------------------------------------------------------------------
void uiPopFlatStyle() {
	ImGui::PopStyleColor(7);
	ImGui::PopStyleVar(2);
}

// ------------------------------------------------------------------------------------------------
void uiCreateDockSpace(AppState& state) {
    const ImGuiViewport* viewport = ImGui::GetMainViewport();
    static ImGuiDockNodeFlags dockFlags = static_cast<ImGuiDockNodeFlags>(uint32_t(ImGuiDockNodeFlags_None) | uint32_t(ImGuiDockNodeFlags_NoWindowMenuButton));
    ImGuiID dockspace_id = ImGui::GetID("MyDockSpaceId");
    const bool init = ImGui::DockBuilderGetNode(dockspace_id) == NULL;
    if (init)
    {
        ImGui::DockBuilderRemoveNode(dockspace_id);
        ImGui::DockBuilderAddNode(dockspace_id, ImGuiDockNodeFlags_None); //ImGuiDockNodeFlags_DockSpace
        ImGui::DockBuilderSetNodeSize(dockspace_id, viewport->WorkSize);

        ImGuiID dock_id_right = 0;
        ImGuiID dock_id_left = ImGui::DockBuilderSplitNode(dockspace_id, ImGuiDir_Left, 0.25f, nullptr, &dock_id_right);

        ImGuiID dock_id_bottom = 0;
        ImGuiID dock_id_top = ImGui::DockBuilderSplitNode(dock_id_left, ImGuiDir_Up, 0.5f, nullptr, &dock_id_bottom);
        ImGui::DockBuilderDockWindow("Scene", dock_id_top);
        ImGui::DockBuilderDockWindow("Rendering", dock_id_bottom);
        
        ImGui::DockBuilderDockWindow("Render Output", dock_id_right);

        ImGui::DockBuilderFinish(dockspace_id);
    }
    ImGui::DockSpaceOverViewport(dockspace_id, viewport, dockFlags);
}

// ------------------------------------------------------------------------------------------------
bool uiButton(const char* name, const char* help = nullptr, bool disabled = false, ImGuiButtonFlags flags = 0) {
	bool ret = false;

	if (disabled) {
		ImGuiStyle& style = ImGui::GetStyle();
		float buttonalpha = style.Colors[ImGuiCol_Button].w;

		if (buttonalpha == 0.f) // this is usually used for flat style, so we don't want the text to disappear
			buttonalpha = 1.f;
		
		ImVec4 textcolor = ImGui::GetStyle().Colors[ImGuiCol_Text];
		ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(textcolor.x, textcolor.y, textcolor.z, 0.4f * buttonalpha * textcolor.w));
	}

	if(ImGui::ButtonEx(name, ImVec2(0, 0), (disabled ? ImGuiItemFlags_Disabled : 0) | flags)) {
		ret = true;
	}

	if(disabled)
		ImGui::PopStyleColor();

	if(help && ImGui::IsItemHovered()) {
		ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(4.f, 4.f));
		ImGui::SetTooltip("%s", help);
		ImGui::PopStyleVar();
	}

	return ret;
}

// ------------------------------------------------------------------------------------------------
void uiMainMenu(AppState& state) {
    if (ImGui::BeginMainMenuBar()) {
            // Show connection status
            static bool has_ever_connected = false;
            if (state.client) {
                bool is_connected = state.client->connected();
                if (is_connected) {
                    ImGui::TextColored(ImVec4(0.1f, 0.6f, 0.2f, 1.0f), ICON_FA_CIRCLE);
                    has_ever_connected = true;
                } else {
                    if (!has_ever_connected) {
                        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.0f, 1.0f), ICON_FA_CIRCLE);
                    } else {
                        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.0f, 1.0f), ICON_FA_CIRCLE);
                    }
                }
            } else {
                ImGui::TextColored(ImVec4(0.6f, 0.2f, 0.1f, 1.0f), ICON_FA_CIRCLE);
            }

            // Right after status, show asset root and current scene with vertical separators
            uiVSeparator();
            std::string scene_label = "(none)";
            if (!state.sceneFiles.empty()) {
                const size_t idx = static_cast<size_t>(state.selectedSceneIndex) < state.sceneFiles.size() ? static_cast<size_t>(state.selectedSceneIndex) : 0;
                try {
                    scene_label = std::filesystem::path(state.sceneFiles[idx]).stem().string();
                } catch (...) {
                    scene_label = state.sceneFiles[idx];
                }
            }
            ImGui::Text(ICON_FA_FILE " Scene: %s", scene_label.c_str());

            uiVSeparator();
            const std::string asset_root_label = state.assetRoot.empty() ? std::string("(not set)") : state.assetRoot;
            ImGui::Text(ICON_FA_FOLDER " Assets: %s", asset_root_label.c_str());

        ImGui::EndMainMenuBar();
    }
}

// ------------------------------------------------------------------------------------------------
void uiListEntities(AppState& state) {
    static bool update_mesh_entities = true;

    // Entity list
    if (ImGui::CollapsingHeader("Meshes")) {
        if (update_mesh_entities) {
            // Update entity list from the server
            state.client->send_command("list_objects");
            update_mesh_entities = false;
        }

        std::string entity_to_delete = "";

        for (auto& entity : state.meshEntities) {
            ImGui::PushID(entity.name.c_str());
            ImGui::AlignTextToFramePadding();
            
            // Display entity info
            ImGui::Text("%s", entity.name.c_str());
            
            ImGui::SameLine();
            // Delete button
            if (ImGui::Button(ICON_FA_TRASH_CAN)) {
                entity_to_delete = entity.name;
            }
            
            // Show position and scale info
            if (ImGui::DragFloat3("Pos", (float*)&entity.position, 0.1f, -100.0f, 100.0f, "%.3f")) {
                std::string message = fmt::format("move_object {} {:.3f} {:.3f} {:.3f}", entity.name, entity.position.x, entity.position.y, entity.position.z);
                state.client->send_command(message);
            }

            if (ImGui::DragFloat3("Scale", (float*)&entity.scale, 0.1f, 0.1f, 100.0f, "%.3f")) {
                std::string message = fmt::format("scale_object {} {:.3f} {:.3f} {:.3f}", entity.name, entity.scale.x, entity.scale.y, entity.scale.z);
                state.client->send_command(message);
            }
            
            ImGui::Separator();
            ImGui::PopID();
        }
        
        // Handle deletion outside the loop
        if (!entity_to_delete.empty()) {
            std::string message = fmt::format("remove_object {}", entity_to_delete);
            state.client->send_command(message);
//            remove_entity_from_tracking(state, entity_to_delete);
        }
    }
    else {
        // If the entity list is not expanded, we can update it next time
        update_mesh_entities = true;
    }
}

// ------------------------------------------------------------------------------------------------
void uiListLights(AppState& state) {
    static bool update_light_entities = true;

    // Light list
    if (ImGui::CollapsingHeader("Lights")) {
        if (update_light_entities) {
            // Update light list from the server
            state.client->send_command("list_lights");
            update_light_entities = false;
        }

        for (auto& light : state.lightEntities) {
            ImGui::PushID(light.name.c_str());
            ImGui::AlignTextToFramePadding();

            // Display light info
            ImGui::Text("%s", light.name.c_str());

            // Show position, color, and intensity info
            if (ImGui::DragFloat3("Pos", (float*)&light.position, 0.1f, -100.0f, 100.0f, "%.3f")) {
                std::string message = fmt::format("move_light {} {:.3f} {:.3f} {:.3f}", light.name, light.position.x, light.position.y, light.position.z);
                state.client->send_command(message);
            }

            if (ImGui::ColorEdit3("Color", (float*)&light.color)) {
                std::string message = fmt::format("set_light_color {} {:.3f} {:.3f} {:.3f}", light.name, light.color.x, light.color.y, light.color.z);
                state.client->send_command(message);
            }

            if (ImGui::DragFloat("Intensity", &light.intensity, 0.1f, 0.0f, 1000.0f, "%.3f")) {
                std::string message = fmt::format("set_light_intensity {} {:.3f}", light.name, light.intensity);
                state.client->send_command(message);
            }

            ImGui::Separator();
            ImGui::PopID();
        }
    } else {
        // If the light list is not expanded, we can update it next time
        update_light_entities = true;
    }
}

// ------------------------------------------------------------------------------------------------
void uiSceneBuilder(AppState& state) {
    if (ImGui::Begin("Scene")) {
        if (state.sceneFiles.empty() && !state.assetRoot.empty()){
            state.sceneFiles = scan_files(state.assetRoot, JSON_EXTENSIONS);
        }

        if (!state.sceneFiles.empty()) {
            const char* current_scene = state.selectedSceneIndex < state.sceneFiles.size() ? 
                state.sceneFiles[state.selectedSceneIndex].c_str() : state.sceneFiles[0].c_str();

            if (ImGui::BeginCombo("##SceneFile", current_scene)) {
                for (int i = 0; i < state.sceneFiles.size(); i++) {
                    const bool is_selected = (state.selectedSceneIndex == i);
                    if (ImGui::Selectable(state.sceneFiles[i].c_str(), is_selected)) {
                        state.selectedSceneIndex = i;
                    }
                    if (is_selected) {
                        ImGui::SetItemDefaultFocus();
                    }
                }
                ImGui::EndCombo();
            }
            ImGui::SameLine();
            
            if (ImGui::Button("Load Scene")) {
                std::string selected_file = state.sceneFiles[state.selectedSceneIndex];

                // Reset entity lists - this should only be done when loading succeeds
				state.meshEntities.clear();
				state.lightEntities.clear();

                std::string message = fmt::format("load_scene {}", selected_file);
                state.client->send_command(message);

                state.client->send_command("debug_mode");
                state.client->send_command("render_type");
                state.client->send_command("get_camera");

				// save initial camera values
				state.initialCameraPos = state.cameraPos;
				state.initialCameraRotation = state.cameraRotation;
                state.initialCameraFov = state.cameraFov;
            }

            uiListEntities(state);
            uiListLights(state);
        }
    }
    ImGui::End();    
}

// ------------------------------------------------------------------------------------------------
// Parse object list message like: "object_list 7 box_5:0,0.25,0.1:0.1,0.1,0.1 mesh_1:1,0.5,0.1:0.5,0.5,0.5 ..."
std::vector<MeshEntity> parse_object_list(const std::string& message) {
    std::vector<MeshEntity> entities;
    
    // Check if this is an object_list message
    if (message.find("object_list ") != 0) {
        return entities;
    }
    
    // Use istringstream to properly parse the message
    std::istringstream iss(message);
    std::string command;
    int count;
    
    // Parse "object_list" and count
    if (!(iss >> command >> count)) {
        return entities; // Failed to parse command and count
    }
    
    // Parse each object entry
    std::string object_entry;
    while (iss >> object_entry) {
        // Parse format: "name:x,y,z:sx,sy,sz"
        size_t first_colon = object_entry.find(':');
        size_t second_colon = object_entry.find(':', first_colon + 1);
        
        if (first_colon == std::string::npos || second_colon == std::string::npos) {
            continue;
        }
        
        MeshEntity entity;
        entity.name = object_entry.substr(0, first_colon);
        
        // Parse position
        std::string pos_str = object_entry.substr(first_colon + 1, second_colon - first_colon - 1);
        std::replace(pos_str.begin(), pos_str.end(), ',', ' ');
        std::istringstream pos_iss(pos_str);
        pos_iss >> entity.position.x >> entity.position.y >> entity.position.z;
        
        // Parse scale
        std::string scale_str = object_entry.substr(second_colon + 1);
        std::replace(scale_str.begin(), scale_str.end(), ',', ' ');
        std::istringstream scale_iss(scale_str);
        scale_iss >> entity.scale.x >> entity.scale.y >> entity.scale.z;
        
        // Default rotation to zero
        entity.rotation = Vec3(0, 0, 0);
        
        entities.push_back(entity);
    }
    
    return entities;
}

// Add a function to parse the light list response
std::vector<LightEntity> parse_light_list(const std::string& message) {
    std::vector<LightEntity> lights;

    // Check if this is a light_list message
    if (message.find("light_list ") != 0) {
        return lights;
    }

    // Use istringstream to properly parse the message
    std::istringstream iss(message);
    std::string command;
    int count;

    // Parse "light_list" and count
    if (!(iss >> command >> count)) {
        return lights; // Failed to parse command and count
    }

    // Parse each light entry
    std::string light_entry;
    while (iss >> light_entry) {
        // Parse format: "name:x,y,z:r,g,b:intensity"
        size_t first_colon = light_entry.find(':');
        size_t second_colon = light_entry.find(':', first_colon + 1);
        size_t third_colon = light_entry.find(':', second_colon + 1);

        if (first_colon == std::string::npos || second_colon == std::string::npos || third_colon == std::string::npos) {
            continue;
        }

        LightEntity light;
        light.name = light_entry.substr(0, first_colon);

        // Parse position
        std::string pos_str = light_entry.substr(first_colon + 1, second_colon - first_colon - 1);
        std::replace(pos_str.begin(), pos_str.end(), ',', ' ');
        std::istringstream pos_iss(pos_str);
        pos_iss >> light.position.x >> light.position.y >> light.position.z;

        // Parse color
        std::string color_str = light_entry.substr(second_colon + 1, third_colon - second_colon - 1);
        std::replace(color_str.begin(), color_str.end(), ',', ' ');
        std::istringstream color_iss(color_str);
        color_iss >> light.color.x >> light.color.y >> light.color.z;

        // Parse intensity
        std::string intensity_str = light_entry.substr(third_colon + 1);
        light.intensity = std::stof(intensity_str);

        lights.push_back(light);
    }

    return lights;
}

// ------------------------------------------------------------------------------------------------
// Message handling
void handle_server_message(AppState* state, const std::string& message) {
    std::lock_guard<std::mutex> lock(state->ui_queue_mutex);
    state->ui_message_queue.push(message);
}

void handle_status_message(AppState* state, const std::string& message) {
#if UI_LOG_VERBOSE
    std::cout << "[status] " << message << std::endl;
#endif
}

void process_ui_messages(AppState& state) {
    std::lock_guard<std::mutex> lock(state.ui_queue_mutex);
    
    while (!state.ui_message_queue.empty()) {
        std::string message = state.ui_message_queue.front();
        state.ui_message_queue.pop();
        
        // Handle object list messages
        if (message.find("object_list ") == 0) {
            std::vector<MeshEntity> entities = parse_object_list(message);
            state.meshEntities = entities;
            std::cout << "Updated mesh entities: " << entities.size() << " objects" << std::endl;
        }
        // Handle light list messages
        else if (message.find("light_list ") == 0) {
            std::vector<LightEntity> lights = parse_light_list(message);
            state.lightEntities = lights;
            std::cout << "Updated light entities: " << lights.size() << " lights" << std::endl;
        }
        else if (message.find("debug_mode ") == 0) {
            std::string mode = message.substr(std::string("debug_mode ").size());
            const char* debugModes[] = {
                "meshlet", "triangles", "albedo", "normals", "ao", "roughness", "metalness", "vertex_color"
            };
            for (int i = 0; i < IM_ARRAYSIZE(debugModes); ++i) {
                if (mode == debugModes[i]) {
                    state.debugModeIdx = i;
                    break;
                }
            }
        }
        else if (message.find("render_type ") == 0) {
            std::string type = message.substr(std::string("render_type ").size());
            const char* renderTypes[] = {
                "forward", "debug", "pathtracer"
            };
            for (int i = 0; i < IM_ARRAYSIZE(renderTypes); ++i) {
                if (type == renderTypes[i]) {
                    state.renderTypeIdx = i;
                    break;
                }
            }
        }
		else if (message.find("camera_update ") == 0) {
			std::istringstream iss(message);
			std::string cmd;
			float x, y, z, pitch, yaw, roll, fov;
			if (iss >> cmd >> x >> y >> z >> pitch >> yaw >> roll >> fov) {
                state.cameraPos = Vec3(x, y, z);
				state.cameraRotation = Vec3(pitch, yaw, roll);
				state.cameraFov = fov;
				std::cout << "Camera updated: pos(" << x << "," << y << "," << z
					<< ") rot(" << pitch << "," << yaw << ") fov(" << fov << ")" << std::endl;
			}
		}
        else if (message.find("asset_root ") == 0) {
            // Example: "asset_root c:/src/gs-render-v1/tests/assets"
            state.assetRoot = message.substr(std::string("asset_root ").size());
            std::cout << "Asset root updated: " << state.assetRoot << std::endl;
        }
        else {
            std::cout << "UI Message: " << message << std::endl;
        }
    }
}

// ------------------------------------------------------------------------------------------------
void uiRenderingControl(AppState& state) {
        if (ImGui::Begin("Rendering")) {
            // Render Type Dropdown
            const char* renderTypes[] = {
                "forward", "debug", "pathtracer"
            };
            int prevRenderTypeIdx = state.renderTypeIdx;
            if (ImGui::Combo("Render Type", &state.renderTypeIdx, renderTypes, IM_ARRAYSIZE(renderTypes))) {
                if (prevRenderTypeIdx != state.renderTypeIdx) {
                    std::string message = fmt::format("set_render_type {}", renderTypes[state.renderTypeIdx]);
                    state.client->send_command(message);
                }
            }

            if (state.renderTypeIdx == 1) { 
				// Debug Mode Dropdown
				const char* debugModes[] = {
					"meshlet", "triangles", "albedo", "normals", "ao", "roughness", "metalness", "vertex_color"
				};
				int prevDebugModeIdx = state.debugModeIdx;
				if (ImGui::Combo("Debug Mode", &state.debugModeIdx, debugModes, IM_ARRAYSIZE(debugModes))) {
					if (prevDebugModeIdx != state.debugModeIdx) {
						std::string message = fmt::format("set_debug_mode {}", debugModes[state.debugModeIdx]);
						state.client->send_command(message);
					}
				}
			}

			ImGui::Separator();
			ImGui::Text("Camera Controls");

			// Camera Position controls
			if (ImGui::DragFloat3("Camera Position", (float*)&state.cameraPos, 0.1f, -100.0f, 100.0f, "%.3f")) {
				std::string message = fmt::format("camera_pos {:.3f} {:.3f} {:.3f}",
                    state.cameraPos.x, state.cameraPos.y, state.cameraPos.z);
				state.client->send_command(message);
			}

			// Camera Rotation controls (pitch/yaw/roll)
			if (ImGui::DragFloat3("Camera Rotation (P/Y/R)", (float*)&state.cameraRotation, 1.0f, -180.0f, 180.0f, "%.1f")) {
				std::string message = fmt::format("camera_rotation {:.1f} {:.1f} {:.1f}",
                    state.cameraRotation.x, state.cameraRotation.y, state.cameraRotation.z);
				state.client->send_command(message);
			}

			// Camera FOV control
			if (ImGui::SliderFloat("Camera FOV", &state.cameraFov, 5.0f, 60.0f, "%.1f deg")) {
				std::string message = fmt::format("camera_fov {:.1f}", state.cameraFov);
				state.client->send_command(message);
			}

			// Camera Reset button
			if (ImGui::Button("Reset Camera")) {
				state.client->send_command("camera_reset");
				// Reset UI values to defaults
                state.cameraPos = state.initialCameraPos;
                state.cameraRotation = state.initialCameraRotation;
                state.cameraFov = state.initialCameraFov;
			}
        }
        ImGui::End();
}

// ------------------------------------------------------------------------------------------------
// Main function
int main() {
    try {
        // Initialize GLFW
        if (!glfwInit()) {
            UI_ERROR("Failed to initialize GLFW");
            return -1;
        }

        // OpenGL version
        glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
        glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
        glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
#ifdef __APPLE__
        glfwWindowHint(GLFW_OPENGL_FORWARD_COMPAT, GL_TRUE);
#endif

        // Create window
        GLFWwindow* window = glfwCreateWindow(WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_TITLE, NULL, NULL);
        if (!window) {
            UI_ERROR("Failed to create window");
            glfwTerminate();
            return -1;
        }

        glfwMakeContextCurrent(window);
        glfwSwapInterval(1); // Enable vsync

        // Setup ImGui
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO(); (void)io;
        io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;

        // Font configuration
        
        // Add a larger default font
        ImFont* font_regular = io.Fonts->AddFontFromFileTTF(FONT_PATH_REGULAR, FONT_SIZE_REGULAR);

        // Add FontAwesome icons
        static const ImWchar icons_ranges[] = { ICON_MIN_FA, ICON_MAX_FA, 0 };
        ImFontConfig icons_config;
        icons_config.MergeMode = true;
        icons_config.PixelSnapH = true;
        ImFont* font_icons = io.Fonts->AddFontFromFileTTF(FONT_PATH_ICONS, FONT_SIZE_REGULAR, &icons_config, icons_ranges);

        ImFont* font_large = io.Fonts->AddFontFromFileTTF(FONT_PATH_REGULAR, FONT_SIZE_LARGE);
        font_icons = io.Fonts->AddFontFromFileTTF(FONT_PATH_ICONS, FONT_SIZE_LARGE, &icons_config, icons_ranges);

        ImFont* font_small = io.Fonts->AddFontFromFileTTF(FONT_PATH_REGULAR, FONT_SIZE_SMALL);
        font_icons = io.Fonts->AddFontFromFileTTF(FONT_PATH_ICONS, FONT_SIZE_SMALL, &icons_config, icons_ranges);

        ImGui::GetIO().FontDefault	= font_regular;

        ImGui::StyleColorsLight();

        // Setup Platform/Renderer backends
        ImGui_ImplGlfw_InitForOpenGL(window, true);
        ImGui_ImplOpenGL3_Init("#version 150");

        AppState state;
        
        // Create and configure client
        state.client = std::make_unique<RenderingClient>("127.0.0.1", SERVER_PORT, HEARTBEAT_TIMEOUT);
        
        // Set up callbacks
        state.client->set_on_message([&state](const std::string& msg) {
            handle_server_message(&state, msg);
        });

        state.client->set_on_status([&state](const std::string& msg) {
            handle_status_message(&state, msg);
            // Send get_asset_root only when connection is established or re-established
            if (msg.find("Connected") != std::string::npos || msg.find("Reconnected") != std::string::npos) {
                if (state.client && state.client->connected()) {
                    state.client->send_command("get_asset_root");
                }
            }
        });
        
        // Start client
        state.client->start();
        
        ImVec4 clear_color = ImVec4(0.2f, 0.2f, 0.2f, 1.0f);

        // Main loop
        while (!glfwWindowShouldClose(window) && state.running) {
            glfwPollEvents();
            
            // Process UI messages from server
            process_ui_messages(state);
            
            // Start the Dear ImGui frame
            ImGui_ImplOpenGL3_NewFrame();
            ImGui_ImplGlfw_NewFrame();
            ImGui::NewFrame();

            uiPushFlatStyle();
            ImGui::PushFont(font_large);

            // Main menu bar
            uiMainMenu(state);

            // Control window (only show when connected)
            if (state.client && state.client->connected()) {
                uiCreateDockSpace(state);

                uiSceneBuilder(state);
                uiRenderingControl(state);
            }

            ImGui::PopFont();
            uiPopFlatStyle();
            
            // Show demo window for testing
            // static bool show_demo = true;
            // if (show_demo) {
            //     ImGui::ShowDemoWindow(&show_demo);
            // }

            // Rendering
            ImGui::Render();
            int display_w, display_h;
            glfwGetFramebufferSize(window, &display_w, &display_h);
            glViewport(0, 0, display_w, display_h);
            glClearColor(clear_color.x * clear_color.w, clear_color.y * clear_color.w, 
                        clear_color.z * clear_color.w, clear_color.w);
            glClear(GL_COLOR_BUFFER_BIT);
            ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

            glfwSwapBuffers(window);
        }

        // Cleanup
        std::cout << "Shutting down..." << std::endl;
        state.client->stop();

        ImGui_ImplOpenGL3_Shutdown();
        ImGui_ImplGlfw_Shutdown();
        ImGui::DestroyContext();

        glfwDestroyWindow(window);
        glfwTerminate();

        return 0;
    }
    catch (const std::exception& e) {
        UI_ERROR("Exception: " << e.what());
        return -1;
    }
}