# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/src/gs-render-ui

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/src/gs-render-ui/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/src/gs-render-ui/build/CMakeFiles /home/<USER>/src/gs-render-ui/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/src/gs-render-ui/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named g-ui

# Build rule for target.
g-ui: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 g-ui
.PHONY : g-ui

# fast build rule for target.
g-ui/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/build
.PHONY : g-ui/fast

ext/imgui/backends/imgui_impl_glfw.o: ext/imgui/backends/imgui_impl_glfw.cpp.o
.PHONY : ext/imgui/backends/imgui_impl_glfw.o

# target to build an object file
ext/imgui/backends/imgui_impl_glfw.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o
.PHONY : ext/imgui/backends/imgui_impl_glfw.cpp.o

ext/imgui/backends/imgui_impl_glfw.i: ext/imgui/backends/imgui_impl_glfw.cpp.i
.PHONY : ext/imgui/backends/imgui_impl_glfw.i

# target to preprocess a source file
ext/imgui/backends/imgui_impl_glfw.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.i
.PHONY : ext/imgui/backends/imgui_impl_glfw.cpp.i

ext/imgui/backends/imgui_impl_glfw.s: ext/imgui/backends/imgui_impl_glfw.cpp.s
.PHONY : ext/imgui/backends/imgui_impl_glfw.s

# target to generate assembly for a file
ext/imgui/backends/imgui_impl_glfw.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.s
.PHONY : ext/imgui/backends/imgui_impl_glfw.cpp.s

ext/imgui/backends/imgui_impl_opengl3.o: ext/imgui/backends/imgui_impl_opengl3.cpp.o
.PHONY : ext/imgui/backends/imgui_impl_opengl3.o

# target to build an object file
ext/imgui/backends/imgui_impl_opengl3.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o
.PHONY : ext/imgui/backends/imgui_impl_opengl3.cpp.o

ext/imgui/backends/imgui_impl_opengl3.i: ext/imgui/backends/imgui_impl_opengl3.cpp.i
.PHONY : ext/imgui/backends/imgui_impl_opengl3.i

# target to preprocess a source file
ext/imgui/backends/imgui_impl_opengl3.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.i
.PHONY : ext/imgui/backends/imgui_impl_opengl3.cpp.i

ext/imgui/backends/imgui_impl_opengl3.s: ext/imgui/backends/imgui_impl_opengl3.cpp.s
.PHONY : ext/imgui/backends/imgui_impl_opengl3.s

# target to generate assembly for a file
ext/imgui/backends/imgui_impl_opengl3.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.s
.PHONY : ext/imgui/backends/imgui_impl_opengl3.cpp.s

ext/imgui/imgui.o: ext/imgui/imgui.cpp.o
.PHONY : ext/imgui/imgui.o

# target to build an object file
ext/imgui/imgui.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o
.PHONY : ext/imgui/imgui.cpp.o

ext/imgui/imgui.i: ext/imgui/imgui.cpp.i
.PHONY : ext/imgui/imgui.i

# target to preprocess a source file
ext/imgui/imgui.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.i
.PHONY : ext/imgui/imgui.cpp.i

ext/imgui/imgui.s: ext/imgui/imgui.cpp.s
.PHONY : ext/imgui/imgui.s

# target to generate assembly for a file
ext/imgui/imgui.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.s
.PHONY : ext/imgui/imgui.cpp.s

ext/imgui/imgui_demo.o: ext/imgui/imgui_demo.cpp.o
.PHONY : ext/imgui/imgui_demo.o

# target to build an object file
ext/imgui/imgui_demo.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o
.PHONY : ext/imgui/imgui_demo.cpp.o

ext/imgui/imgui_demo.i: ext/imgui/imgui_demo.cpp.i
.PHONY : ext/imgui/imgui_demo.i

# target to preprocess a source file
ext/imgui/imgui_demo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.i
.PHONY : ext/imgui/imgui_demo.cpp.i

ext/imgui/imgui_demo.s: ext/imgui/imgui_demo.cpp.s
.PHONY : ext/imgui/imgui_demo.s

# target to generate assembly for a file
ext/imgui/imgui_demo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.s
.PHONY : ext/imgui/imgui_demo.cpp.s

ext/imgui/imgui_draw.o: ext/imgui/imgui_draw.cpp.o
.PHONY : ext/imgui/imgui_draw.o

# target to build an object file
ext/imgui/imgui_draw.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o
.PHONY : ext/imgui/imgui_draw.cpp.o

ext/imgui/imgui_draw.i: ext/imgui/imgui_draw.cpp.i
.PHONY : ext/imgui/imgui_draw.i

# target to preprocess a source file
ext/imgui/imgui_draw.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.i
.PHONY : ext/imgui/imgui_draw.cpp.i

ext/imgui/imgui_draw.s: ext/imgui/imgui_draw.cpp.s
.PHONY : ext/imgui/imgui_draw.s

# target to generate assembly for a file
ext/imgui/imgui_draw.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.s
.PHONY : ext/imgui/imgui_draw.cpp.s

ext/imgui/imgui_tables.o: ext/imgui/imgui_tables.cpp.o
.PHONY : ext/imgui/imgui_tables.o

# target to build an object file
ext/imgui/imgui_tables.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o
.PHONY : ext/imgui/imgui_tables.cpp.o

ext/imgui/imgui_tables.i: ext/imgui/imgui_tables.cpp.i
.PHONY : ext/imgui/imgui_tables.i

# target to preprocess a source file
ext/imgui/imgui_tables.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.i
.PHONY : ext/imgui/imgui_tables.cpp.i

ext/imgui/imgui_tables.s: ext/imgui/imgui_tables.cpp.s
.PHONY : ext/imgui/imgui_tables.s

# target to generate assembly for a file
ext/imgui/imgui_tables.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.s
.PHONY : ext/imgui/imgui_tables.cpp.s

ext/imgui/imgui_widgets.o: ext/imgui/imgui_widgets.cpp.o
.PHONY : ext/imgui/imgui_widgets.o

# target to build an object file
ext/imgui/imgui_widgets.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o
.PHONY : ext/imgui/imgui_widgets.cpp.o

ext/imgui/imgui_widgets.i: ext/imgui/imgui_widgets.cpp.i
.PHONY : ext/imgui/imgui_widgets.i

# target to preprocess a source file
ext/imgui/imgui_widgets.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.i
.PHONY : ext/imgui/imgui_widgets.cpp.i

ext/imgui/imgui_widgets.s: ext/imgui/imgui_widgets.cpp.s
.PHONY : ext/imgui/imgui_widgets.s

# target to generate assembly for a file
ext/imgui/imgui_widgets.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.s
.PHONY : ext/imgui/imgui_widgets.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/g-ui.dir/build.make CMakeFiles/g-ui.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... g-ui"
	@echo "... ext/imgui/backends/imgui_impl_glfw.o"
	@echo "... ext/imgui/backends/imgui_impl_glfw.i"
	@echo "... ext/imgui/backends/imgui_impl_glfw.s"
	@echo "... ext/imgui/backends/imgui_impl_opengl3.o"
	@echo "... ext/imgui/backends/imgui_impl_opengl3.i"
	@echo "... ext/imgui/backends/imgui_impl_opengl3.s"
	@echo "... ext/imgui/imgui.o"
	@echo "... ext/imgui/imgui.i"
	@echo "... ext/imgui/imgui.s"
	@echo "... ext/imgui/imgui_demo.o"
	@echo "... ext/imgui/imgui_demo.i"
	@echo "... ext/imgui/imgui_demo.s"
	@echo "... ext/imgui/imgui_draw.o"
	@echo "... ext/imgui/imgui_draw.i"
	@echo "... ext/imgui/imgui_draw.s"
	@echo "... ext/imgui/imgui_tables.o"
	@echo "... ext/imgui/imgui_tables.i"
	@echo "... ext/imgui/imgui_tables.s"
	@echo "... ext/imgui/imgui_widgets.o"
	@echo "... ext/imgui/imgui_widgets.i"
	@echo "... ext/imgui/imgui_widgets.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

