
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_glfw.cpp" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_opengl3.cpp" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/imgui.cpp" "CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/imgui_demo.cpp" "CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp" "CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/imgui_tables.cpp" "CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/ext/imgui/imgui_widgets.cpp" "CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o" "gcc" "CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o.d"
  "/home/<USER>/src/gs-render-ui/main.cpp" "CMakeFiles/g-ui.dir/main.cpp.o" "gcc" "CMakeFiles/g-ui.dir/main.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
