/usr/bin/c++ "CMakeFiles/g-ui.dir/main.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o" "CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o" -o g-ui  /usr/lib/x86_64-linux-gnu/libglfw.so.3.3 -lfmt -ldl /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libOpenGL.so 
