CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o: \
 /home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/src/gs-render-ui/ext/imgui/imgui.h \
 /home/<USER>/src/gs-render-ui/ext/imgui/imconfig.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/float.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h /usr/include/string.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/strings.h /usr/include/assert.h \
 /home/<USER>/src/gs-render-ui/ext/imgui/imgui_internal.h \
 /usr/include/stdio.h /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/c++/13/stdlib.h /usr/include/c++/13/cstdlib \
 /usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
 /usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
 /usr/include/c++/13/pstl/pstl_config.h /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/13/bits/std_abs.h /usr/include/c++/13/math.h \
 /usr/include/c++/13/cmath /usr/include/c++/13/bits/requires_hosted.h \
 /usr/include/c++/13/bits/cpp_type_traits.h \
 /usr/include/c++/13/ext/type_traits.h /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/c++/13/bits/specfun.h \
 /usr/include/c++/13/bits/stl_algobase.h \
 /usr/include/c++/13/bits/functexcept.h \
 /usr/include/c++/13/bits/exception_defines.h \
 /usr/include/c++/13/ext/numeric_traits.h \
 /usr/include/c++/13/bits/stl_pair.h /usr/include/c++/13/type_traits \
 /usr/include/c++/13/bits/move.h /usr/include/c++/13/bits/utility.h \
 /usr/include/c++/13/compare /usr/include/c++/13/concepts \
 /usr/include/c++/13/bits/stl_iterator_base_types.h \
 /usr/include/c++/13/bits/iterator_concepts.h \
 /usr/include/c++/13/bits/ptr_traits.h \
 /usr/include/c++/13/bits/ranges_cmp.h \
 /usr/include/c++/13/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/13/bits/concept_check.h \
 /usr/include/c++/13/debug/assertions.h \
 /usr/include/c++/13/bits/stl_iterator.h /usr/include/c++/13/new \
 /usr/include/c++/13/bits/exception.h \
 /usr/include/c++/13/bits/stl_construct.h \
 /usr/include/c++/13/debug/debug.h \
 /usr/include/c++/13/bits/predefined_ops.h /usr/include/c++/13/bit \
 /usr/include/c++/13/limits /usr/include/c++/13/tr1/gamma.tcc \
 /usr/include/c++/13/tr1/special_function_util.h \
 /usr/include/c++/13/tr1/bessel_function.tcc \
 /usr/include/c++/13/tr1/beta_function.tcc \
 /usr/include/c++/13/tr1/ell_integral.tcc \
 /usr/include/c++/13/tr1/exp_integral.tcc \
 /usr/include/c++/13/tr1/hypergeometric.tcc \
 /usr/include/c++/13/tr1/legendre_function.tcc \
 /usr/include/c++/13/tr1/modified_bessel_func.tcc \
 /usr/include/c++/13/tr1/poly_hermite.tcc \
 /usr/include/c++/13/tr1/poly_laguerre.tcc \
 /usr/include/c++/13/tr1/riemann_zeta.tcc \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/immintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/x86gprintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/ia32intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/adxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/bmiintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/bmi2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/cetintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/cldemoteintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/clflushoptintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/clwbintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/clzerointrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/cmpccxaddintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/enqcmdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/fxsrintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/lzcntintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/lwpintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/movdirintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/mwaitintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/mwaitxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/pconfigintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/popcntintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/pkuintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/prfchiintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/raointintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/rdseedintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/rtmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/serializeintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/sgxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/tbmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/tsxldtrkintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/uintrintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/waitpkgintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/wbnoinvdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xsaveintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xsavecintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xsaveoptintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xsavesintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xtestintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/hresetintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/mmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/xmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/mm_malloc.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/emmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/pmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/tmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/smmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/wmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avxvnniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avxifmaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avxvnniint8intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512erintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512pfintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512cdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512dqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vlbwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vldqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512ifmaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512ifmavlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmiintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmivlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx5124fmapsintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx5124vnniwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vpopcntdqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmi2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmi2vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vnniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vnnivlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vpopcntdqvlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bitalgintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vp2intersectintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vp2intersectvlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fp16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fp16vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/shaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/fmaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/f16cintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/gfniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/vaesintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/vpclmulqdqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bf16vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bf16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/avxneconvertintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/amxtileintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/amxint8intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/amxbf16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/amxcomplexintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/prfchwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/keylockerintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/amxfp16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-least.h \
 /home/<USER>/src/gs-render-ui/ext/imgui/imstb_rectpack.h \
 /home/<USER>/src/gs-render-ui/ext/imgui/imstb_truetype.h
