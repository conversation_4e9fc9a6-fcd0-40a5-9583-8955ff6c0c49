# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/src/gs-render-ui

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/src/gs-render-ui/build

# Include any dependencies generated for this target.
include CMakeFiles/g-ui.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/g-ui.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/g-ui.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/g-ui.dir/flags.make

CMakeFiles/g-ui.dir/main.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/main.cpp.o: /home/<USER>/src/gs-render-ui/main.cpp
CMakeFiles/g-ui.dir/main.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/g-ui.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/main.cpp.o -MF CMakeFiles/g-ui.dir/main.cpp.o.d -o CMakeFiles/g-ui.dir/main.cpp.o -c /home/<USER>/src/gs-render-ui/main.cpp

CMakeFiles/g-ui.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/main.cpp > CMakeFiles/g-ui.dir/main.cpp.i

CMakeFiles/g-ui.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/main.cpp -o CMakeFiles/g-ui.dir/main.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/imgui.cpp
CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/imgui.cpp

CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/imgui.cpp > CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/imgui.cpp -o CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/imgui_demo.cpp
CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/imgui_demo.cpp

CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/imgui_demo.cpp > CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/imgui_demo.cpp -o CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp
CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp

CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp > CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/imgui_draw.cpp -o CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/imgui_tables.cpp
CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/imgui_tables.cpp

CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/imgui_tables.cpp > CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/imgui_tables.cpp -o CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/imgui_widgets.cpp
CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/imgui_widgets.cpp

CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/imgui_widgets.cpp > CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/imgui_widgets.cpp -o CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_glfw.cpp
CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_glfw.cpp

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_glfw.cpp > CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_glfw.cpp -o CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.s

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o: CMakeFiles/g-ui.dir/flags.make
CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o: /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_opengl3.cpp
CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o: CMakeFiles/g-ui.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o -MF CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o.d -o CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o -c /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_opengl3.cpp

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_opengl3.cpp > CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.i

CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/src/gs-render-ui/ext/imgui/backends/imgui_impl_opengl3.cpp -o CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.s

# Object files for target g-ui
g__ui_OBJECTS = \
"CMakeFiles/g-ui.dir/main.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o" \
"CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o"

# External object files for target g-ui
g__ui_EXTERNAL_OBJECTS =

g-ui: CMakeFiles/g-ui.dir/main.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/imgui.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/imgui_demo.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/imgui_draw.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/imgui_tables.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/imgui_widgets.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_glfw.cpp.o
g-ui: CMakeFiles/g-ui.dir/ext/imgui/backends/imgui_impl_opengl3.cpp.o
g-ui: CMakeFiles/g-ui.dir/build.make
g-ui: /usr/lib/x86_64-linux-gnu/libglfw.so.3.3
g-ui: /usr/lib/x86_64-linux-gnu/libGLX.so
g-ui: /usr/lib/x86_64-linux-gnu/libOpenGL.so
g-ui: CMakeFiles/g-ui.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/src/gs-render-ui/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX executable g-ui"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/g-ui.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/g-ui.dir/build: g-ui
.PHONY : CMakeFiles/g-ui.dir/build

CMakeFiles/g-ui.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/g-ui.dir/cmake_clean.cmake
.PHONY : CMakeFiles/g-ui.dir/clean

CMakeFiles/g-ui.dir/depend:
	cd /home/<USER>/src/gs-render-ui/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/src/gs-render-ui /home/<USER>/src/gs-render-ui /home/<USER>/src/gs-render-ui/build /home/<USER>/src/gs-render-ui/build /home/<USER>/src/gs-render-ui/build/CMakeFiles/g-ui.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/g-ui.dir/depend

