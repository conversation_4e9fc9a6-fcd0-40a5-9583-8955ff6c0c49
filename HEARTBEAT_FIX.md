# Heartbeat Fix Implementation

## Problem Description

The UI client was experiencing unnecessary reconnections due to a mismatch between client and server heartbeat behavior:

### Server Behavior
- Server sends heartbeats **only during idle periods** (no client messages)
- If client sends messages, server **skips heartbeats** until next idle period
- Heartbeats are sent every `kHeartbeatSec` seconds of **true idle time**

### Client Behavior (Before Fix)
- Client expected heartbeats **every 10 seconds** regardless of activity
- Client only reset heartbeat timer on **incoming messages**
- <PERSON><PERSON> would **timeout and reconnect** even when connection was healthy
- This caused unnecessary reconnections during active communication

## Solution Implemented

### 1. Bidirectional Heartbeat Timer Reset

**File**: `main.cpp`  
**Method**: `RenderingClient::send_internal()`

```cpp
// Reset heartbeat timer on outgoing traffic
// This prevents unnecessary reconnections when the connection is healthy
// The server only sends heartbeats during idle periods, so we need to
// account for our own outgoing traffic
last_rx_ = std::chrono::steady_clock::now();

// Debug logging for heartbeat behavior (only in verbose mode)
if (UI_LOG_VERBOSE) {
    log("Sent message, reset heartbeat timer");
}
```

### 2. Enhanced Documentation

**File**: `main.cpp`  
**Method**: `RenderingClient::reader_loop()`

```cpp
// Reset heartbeat timer on incoming traffic
// This works together with the reset in send_internal() to provide
// bidirectional heartbeat tracking that matches the server's behavior
last_rx_ = std::chrono::steady_clock::now();
```

**File**: `main.cpp`  
**Method**: `RenderingClient::heartbeat_loop()`

```cpp
// Check for heartbeat timeout
// Note: last_rx_ is reset on both incoming messages AND outgoing traffic
// This matches the server's behavior of only sending heartbeats during idle periods
```

## How It Works Now

### Before Fix
```
Timeline: 0s ----5s----10s----15s----20s
Client:    |     |     |     |     |
          Send   |     |     |     |
                 |   Timeout!       |
                 |   Reconnect      |
```

### After Fix
```
Timeline: 0s ----5s----10s----15s----20s
Client:    |     |     |     |     |
          Send   |     |     |     |
          Reset  |     |     |     |
                 |   Send           |
                 |   Reset          |
                 |     |     |     |
```

## Benefits

1. **Eliminates Unnecessary Reconnections**: Client no longer times out during active communication
2. **Matches Server Behavior**: Client now correctly accounts for server's idle-only heartbeat policy
3. **Improved Stability**: Connection remains stable during bidirectional traffic
4. **Better Performance**: No more connection teardown/rebuild cycles during normal operation
5. **Debug Visibility**: Added logging to monitor heartbeat behavior (when `UI_LOG_VERBOSE=1`)

## Testing

To verify the fix works:

1. **Build and run** the application
2. **Send commands** to the server (e.g., camera movements, object listings)
3. **Monitor logs** for "Sent message, reset heartbeat timer" messages
4. **Verify** no unnecessary reconnections occur during active communication
5. **Confirm** heartbeats are still received during idle periods

## Configuration

The heartbeat timeout is configurable via:
- **Compile-time**: `#define HEARTBEAT_TIMEOUT 10.0f`
- **Runtime**: `RenderingClient` constructor parameter
- **Default**: 10 seconds

## Related Code

- **Heartbeat Loop**: `RenderingClient::heartbeat_loop()` (lines ~418-470)
- **Reader Loop**: `RenderingClient::reader_loop()` (lines ~375-417)
- **Send Methods**: `RenderingClient::send_internal()` (lines ~320-350)
- **Connection Management**: `RenderingClient::connect_internal()` (lines ~220-280)
