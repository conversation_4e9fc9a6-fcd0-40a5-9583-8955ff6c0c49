{"version": "2.0.0", "tasks": [{"label": "Build Project", "type": "shell", "command": "mkdir -p build && cd build && cmake .. && make -j$(nproc)", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "Clean Build", "type": "shell", "command": "rm -rf build && mkdir -p build && cd build && cmake .. && make -j$(nproc)", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$gcc"], "options": {"cwd": "${workspaceFolder}"}}]}