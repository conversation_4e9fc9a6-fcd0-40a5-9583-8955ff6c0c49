{"version": "0.2.0", "configurations": [{"name": "Launch GUI", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/g-ui", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Build Project", "miDebuggerPath": "/usr/bin/gdb"}, {"name": "Launch GUI (No Build)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/g-ui", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}]}