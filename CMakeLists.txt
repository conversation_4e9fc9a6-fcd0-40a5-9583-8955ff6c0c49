cmake_minimum_required(VERSION 3.16)
project(gui)

set(CMAKE_CXX_STANDARD 20)

# Find required packages
find_package(OpenGL REQUIRED)
find_package(glfw3 REQUIRED)
find_package(Threads REQUIRED)
find_package(fmt REQUIRED)

# Path to ImGui
set(imgui_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/ext/imgui)
#Path to fontawesome
set(fontawesome_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/ext/fontawesome)

# ImGui sources
set(IMGUI_SOURCES
    ${imgui_SOURCE_DIR}/imgui.cpp
    ${imgui_SOURCE_DIR}/imgui_demo.cpp
    ${imgui_SOURCE_DIR}/imgui_draw.cpp
    ${imgui_SOURCE_DIR}/imgui_tables.cpp
    ${imgui_SOURCE_DIR}/imgui_widgets.cpp
    ${imgui_SOURCE_DIR}/backends/imgui_impl_glfw.cpp
    ${imgui_SOURCE_DIR}/backends/imgui_impl_opengl3.cpp
)

# Create executable
add_executable(g-ui main.cpp ${IMGUI_SOURCES})

# Include directories
target_include_directories(g-ui PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/ext
    ${fontawesome_SOURCE_DIR}
    ${imgui_SOURCE_DIR}
    ${imgui_SOURCE_DIR}/backends
)

# Link libraries
target_link_libraries(g-ui
    OpenGL::GL
    glfw
    fmt
    Threads::Threads
    ${CMAKE_DL_LIBS}
)

# Platform-specific settings
if(APPLE)
    target_link_libraries(g-ui "-framework Cocoa" "-framework IOKit")
endif()